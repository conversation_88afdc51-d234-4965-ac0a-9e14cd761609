package com.airdoc.mpd.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.wifi.WifiManager
import com.airdoc.component.common.log.Logger
import java.net.Inet4Address
import java.net.NetworkInterface
import java.util.*

/**
 * FileName: CommonUtils
 * Author by lilin,Date on 2024/11/23 9:25
 * PS: Not easy to write code, please indicate.
 */
object CommonUtils {

    /**
     * 是否是有效的国内手机号
     */
    fun isValidChinesePhoneNumber(phoneNumber: String): Boolean {
        val pattern = "^1[3-9]\\d{9}$".toRegex()
        return pattern.matches(phoneNumber)
    }

    /**
     * 是否是有效的国际手机号
     */
    fun isValidInternationalPhoneNumber(phoneNumber: String): Boolean {
        val pattern = "^\\+?[1-9]\\d{1,14}\$".toRegex()
        return pattern.matches(phoneNumber)
    }

}

/**
 * 网络工具类
 */
object NetworkUtils {

    private val TAG = "NetworkUtils"
    private var currentWebSocketAddress: String = ""

    /**
     * 获取设备IP地址
     */
    fun getDeviceIPAddress(): String {
        // 直接返回localhost
        Logger.d(TAG, msg = "使用localhost作为WebSocket服务器地址")
        return "************"
    }

    /**
     * 设置当前WebSocket地址
     */
    fun setCurrentWebSocketAddress(address: String) {
        currentWebSocketAddress = address
        Logger.d(TAG, msg = "设置WebSocket地址: $address")
    }

    /**
     * 获取当前WebSocket地址
     */
    fun getCurrentWebSocketAddress(): String {
        return currentWebSocketAddress
    }

    /**
     * 检查网络连接状态
     */
    fun isNetworkAvailable(context: Context): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities = connectivityManager.getNetworkCapabilities(network) ?: return false

        return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
}