package com.airdoc.mpd.device

import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.device.bean.DeviceInfo
import com.airdoc.mpd.device.enumeration.BillingMode
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * FileName: DeviceReminderManager
 * Author by lilin,Date on 2025/8/1
 * PS: Not easy to write code, please indicate.
 * 设备信息提醒管理器
 */
object DeviceReminderManager {

    private const val TAG = "DeviceReminderManager"
    
    // 提醒阈值
    private const val EXPIRY_REMINDER_DAYS = 1000  // 提前14天提醒
    private const val USAGE_REMINDER_COUNT = 50  // 剩余50次提醒
    
    // 日期格式
    private val dateFormat = SimpleDateFormat("yyyy.MM.dd", Locale.getDefault())
    
    /**
     * 检查是否需要显示设备提醒
     * @return DeviceReminderInfo 如果需要提醒则返回提醒信息，否则返回null
     */
    fun checkDeviceReminder(): DeviceReminderInfo? {
        val deviceInfo = DeviceManager.getDeviceInfo() ?: return null
        
        // 检查是否今天已经提醒过
        if (hasShownReminderToday()) {
            Logger.d(TAG, msg = "今天已经显示过设备提醒，跳过")
            return null
        }
        
        // 检查设备是否可用
        if (!DeviceManager.isAvailable(deviceInfo)) {
            Logger.d(TAG, msg = "设备不可用，不显示提醒")
            return null
        }
        
        // 检查到期时间提醒
        val expiryReminder = checkExpiryReminder(deviceInfo)
        if (expiryReminder != null) {
            return expiryReminder
        }
        
        // 检查使用次数提醒
        val usageReminder = checkUsageReminder(deviceInfo)
        if (usageReminder != null) {
            return usageReminder
        }
        
        return null
    }
    
    /**
     * 检查到期时间提醒
     */
    private fun checkExpiryReminder(deviceInfo: DeviceInfo): DeviceReminderInfo? {
        val expiryDateStr = deviceInfo.expiryDate ?: return null
        
        try {
            val expiryDate = dateFormat.parse(expiryDateStr) ?: return null
            val currentDate = Date()
            
            // 计算剩余天数
            val diffInMillis = expiryDate.time - currentDate.time
            val remainingDays = TimeUnit.MILLISECONDS.toDays(diffInMillis)
            
            Logger.d(TAG, msg = "设备到期日期: $expiryDateStr, 剩余天数: $remainingDays")
            
            // 如果剩余天数小于等于14天且大于0天，显示提醒
            if (remainingDays in 1..EXPIRY_REMINDER_DAYS) {
                return DeviceReminderInfo(
                    type = DeviceReminderType.EXPIRY,
                    message = "您的设备将于${remainingDays}天后到期，请及时联系商务处理。"
                )
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "解析到期日期失败: $expiryDateStr, error: ${e.message}")
        }
        
        return null
    }
    
    /**
     * 检查使用次数提醒
     */
    private fun checkUsageReminder(deviceInfo: DeviceInfo): DeviceReminderInfo? {
        // 只有计次模式才检查使用次数
        if (DeviceManager.getBillingMode(deviceInfo) != BillingMode.COUNT_BASED) {
            return null
        }
        
        val surplus = deviceInfo.surplus ?: return null
        
        Logger.d(TAG, msg = "设备剩余次数: $surplus")
        
        // 如果剩余次数小于等于50次且大于0次，显示提醒
        if (surplus in 1..USAGE_REMINDER_COUNT) {
            return DeviceReminderInfo(
                type = DeviceReminderType.USAGE,
                message = "您的设备检测次数仅剩余${surplus}次，请及时联系商务充值。"
            )
        }
        
        return null
    }
    
    /**
     * 检查今天是否已经显示过提醒
     */
    private fun hasShownReminderToday(): Boolean {
        val lastShowDate = MMKVManager.decodeString(CommonPreference.DEVICE_REMINDER_LAST_SHOW_DATE)
        val today = dateFormat.format(Date())
        
        return lastShowDate == today
    }
    
    /**
     * 标记今天已经显示过提醒
     */
    fun markReminderShownToday() {
        val today = dateFormat.format(Date())
        MMKVManager.encodeString(CommonPreference.DEVICE_REMINDER_LAST_SHOW_DATE, today)
        Logger.d(TAG, msg = "标记设备提醒已显示: $today")
    }
    
    /**
     * 重置提醒状态（用于测试）
     */
    fun resetReminderStatus() {
        MMKVManager.encodeString(CommonPreference.DEVICE_REMINDER_LAST_SHOW_DATE, null)
        Logger.d(TAG, msg = "重置设备提醒状态")
    }
}

/**
 * 设备提醒信息
 */
data class DeviceReminderInfo(
    val type: DeviceReminderType,
    val message: String
)

/**
 * 设备提醒类型
 */
enum class DeviceReminderType {
    EXPIRY,  // 到期提醒
    USAGE    // 使用次数提醒
}
