<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="380dp"
    android:layout_height="335dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/ic_device_info_dialog_bg">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="设备信息提醒"
        android:textColor="@color/color_333333"
        android:textSize="20sp"
        android:includeFontPadding="false"
        android:layout_marginTop="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_left"
        android:layout_marginEnd="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintRight_toLeftOf="@+id/tv_title"/>

    <ImageView
        android:layout_width="48dp"
        android:layout_height="8dp"
        android:src="@drawable/ic_device_info_dialog_right"
        android:layout_marginStart="10dp"
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toRightOf="@+id/tv_title"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="357dp"
        android:layout_height="211dp"
        android:background="@drawable/common_white_round_20_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title">

        <TextView
            android:id="@+id/tv_open_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="56dp"
            android:includeFontPadding="false"
            android:textColor="@color/color_333333"
            android:textSize="13sp"
            app:layout_constraintHorizontal_bias="0.345"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="您的设备将于X天后到期，请及时联系商务处理。" />

        <TextView
            android:id="@+id/tv_valid_until"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:includeFontPadding="false"
            android:textColor="@color/color_333333"
            android:textSize="13sp"
            app:layout_constraintHorizontal_bias="0.509"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_open_date"
            tools:text="您的设备检测次数仅剩余Ⅹ次，请及时联系商务充值。" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="25dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:text="取消"
            android:textColor="@color/color_333333"
            android:textSize="17sp"
            android:gravity="center"
            android:background="@drawable/common_stroke_round_bg"
            android:focusable="true"
            android:layout_marginEnd="20dp" />

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="100dp"
            android:layout_height="40dp"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="17sp"
            android:gravity="center"
            android:background="@drawable/common_eb4e89_round_bg"
            android:focusable="true" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
