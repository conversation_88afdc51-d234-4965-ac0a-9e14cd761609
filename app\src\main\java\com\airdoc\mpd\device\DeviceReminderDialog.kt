package com.airdoc.mpd.device

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.core.view.isVisible
import com.airdoc.component.common.ktx.setOnSingleClickListener
import com.airdoc.component.common.log.Logger
import com.airdoc.mpd.R
import com.airdoc.mpd.databinding.DialogDeviceReminderBinding


class DeviceReminderDialog(
    context: Context,
    private val reminderInfo: DeviceReminderInfo,
    private val onConfirm: (() -> Unit)? = null,
    private val onCancel: (() -> Unit)? = null
) : Dialog(context, R.style.Theme_Mpd) {

    companion object {
        private const val TAG = "DeviceReminderDialog"
    }

    private lateinit var binding: DialogDeviceReminderBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogDeviceReminderBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        initWindow()
        initView()
        initListener()
    }

    private fun initWindow() {
        window?.let { window ->
            val layoutParams = window.attributes
            layoutParams.gravity = Gravity.CENTER
            layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT
            layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT
            window.attributes = layoutParams
        }
        setCancelable(false)
        setCanceledOnTouchOutside(false)
    }

    private fun initView() {
        Logger.d(TAG, msg = "显示设备提醒: ${reminderInfo.type}, 消息: ${reminderInfo.message}")
        
        // 根据提醒类型显示对应的消息
        when (reminderInfo.type) {
            DeviceReminderType.EXPIRY -> {
                binding.tvExpiryReminder.isVisible = true
                binding.tvUsageReminder.isVisible = false
                binding.tvExpiryReminder.text = reminderInfo.message
            }
            DeviceReminderType.USAGE -> {
                binding.tvExpiryReminder.isVisible = false
                binding.tvUsageReminder.isVisible = true
                binding.tvUsageReminder.text = reminderInfo.message
            }
        }
    }

    private fun initListener() {
        // 取消按钮
        binding.tvCancel.setOnSingleClickListener {
            Logger.d(TAG, msg = "用户点击取消按钮")
            dismiss()
            onCancel?.invoke()
        }

        // 确定按钮
        binding.tvConfirm.setOnSingleClickListener {
            Logger.d(TAG, msg = "用户点击确定按钮")
            // 标记今天已经显示过提醒
            DeviceReminderManager.markReminderShownToday()
            dismiss()
            onConfirm?.invoke()
        }
    }

    override fun show() {
        try {
            super.show()
            Logger.d(TAG, msg = "设备提醒对话框已显示")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "显示设备提醒对话框失败: ${e.message}")
        }
    }

    override fun dismiss() {
        try {
            super.dismiss()
            Logger.d(TAG, msg = "设备提醒对话框已关闭")
        } catch (e: Exception) {
            Logger.e(TAG, msg = "关闭设备提醒对话框失败: ${e.message}")
        }
    }
}
